package ai.zencoder.plugin.auth;

/**
 * 认证信息不可用异常
 * 当无法获取有效的认证信息时抛出此异常
 */
public class NoAuthInfoException extends Exception {
    
    public NoAuthInfoException() {
        super("No authentication information available");
    }
    
    public NoAuthInfoException(String message) {
        super(message);
    }
    
    public NoAuthInfoException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public NoAuthInfoException(Throwable cause) {
        super(cause);
    }
}
