package ai.zencoder.plugin.auth;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 认证服务接口
 * 定义了用户认证相关的核心方法
 */
public interface AuthService {
    
    /**
     * 获取当前访问令牌
     * @return 访问令牌
     * @throws NoAuthInfoException 如果没有有效的认证信息
     */
    @NotNull
    String h() throws NoAuthInfoException;
    
    /**
     * 获取当前认证信息（必须有效）
     * @return 认证信息
     * @throws NoAuthInfoException 如果没有有效的认证信息
     */
    @NotNull
    AuthInfo b() throws NoAuthInfoException;
    
    /**
     * 获取当前认证信息（可能为空）
     * @return 认证信息，如果没有则返回null
     */
    @Nullable
    AuthInfo a();
    
    /**
     * 获取当前用户数据
     * @return 用户数据，如果没有则返回null
     */
    @Nullable
    UserData getUserData();
    
    /**
     * 执行登录操作
     */
    void signIn();
    
    /**
     * 执行注册操作
     */
    void signUp();
    
    /**
     * 检查是否已认证
     * @return true如果已认证，false否则
     */
    boolean isAuthenticated();
    
    /**
     * 重置认证状态
     */
    void resetAuthentication();
    
    /**
     * 刷新认证信息
     * @param expiredAccessToken 过期的访问令牌
     * @return 新的认证信息
     */
    @NotNull
    AuthInfo refreshAuthentication(@NotNull String expiredAccessToken);
    
    /**
     * 登出
     */
    void signOut();
    
    /**
     * 检查令牌是否有效
     * @param token 要检查的令牌
     * @return true如果有效，false否则
     */
    boolean isTokenValid(@NotNull String token);
}
